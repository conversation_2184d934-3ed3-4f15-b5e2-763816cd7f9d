<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Submission extends Model
{
    protected $fillable = [
        'exercise_id',
        'user_id',
        'answers',
        'code_submission',
        'status',
        'score',
        'attempt_number',
        'feedback',
        'test_results',
        'execution_time',
        'submitted_at',
        'graded_at',
    ];

    protected $casts = [
        'answers' => 'array',
        'test_results' => 'array',
        'submitted_at' => 'datetime',
        'graded_at' => 'datetime',
    ];

    public function exercise(): BelongsTo
    {
        return $this->belongsTo(Exercise::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
