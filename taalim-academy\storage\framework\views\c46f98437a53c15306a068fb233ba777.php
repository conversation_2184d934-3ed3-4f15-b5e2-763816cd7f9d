<?php $__env->startSection('header'); ?>
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        فئات المحتوى
    </h2>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">استكشف فئات المحتوى</h1>
            <p class="text-lg text-gray-600">
                اختر الفئة التي تهمك لاستكشاف الدروس والوثائق والتمارين المتعلقة بها
            </p>
        </div>

        <!-- Categories Grid -->
        <?php if($categories->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition duration-300 ease-in-out transform hover:-translate-y-1">
                        <div class="p-6">
                            <!-- Category Header -->
                            <div class="flex items-center mb-4">
                                <?php if($category->icon): ?>
                                    <div class="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center bg-gray-100">
                                        <i class="<?php echo e($category->icon); ?> text-2xl text-gray-600"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center bg-blue-600">
                                        <span class="text-white font-bold text-lg"><?php echo e(substr($category->name_ar, 0, 1)); ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="mr-4">
                                    <h3 class="text-xl font-semibold text-gray-900"><?php echo e($category->name_ar); ?></h3>
                                    <?php if($category->name_en): ?>
                                        <p class="text-sm text-gray-500"><?php echo e($category->name_en); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Category Description -->
                            <?php if($category->description_ar): ?>
                                <p class="text-gray-600 mb-6 leading-relaxed">
                                    <?php echo e(Str::limit($category->description_ar, 120)); ?>

                                </p>
                            <?php endif; ?>

                            <!-- Category Stats -->
                            <div class="grid grid-cols-3 gap-4 mb-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">
                                        <?php echo e($category->lessons()->where('is_published', true)->count()); ?>

                                    </div>
                                    <div class="text-xs text-gray-500">درس</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">
                                        <?php echo e($category->documentation()->where('is_published', true)->count()); ?>

                                    </div>
                                    <div class="text-xs text-gray-500">وثيقة</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">
                                        <?php echo e($category->exercises()->where('is_published', true)->count()); ?>

                                    </div>
                                    <div class="text-xs text-gray-500">تمرين</div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-2">
                                <a href="<?php echo e(route('search', ['category' => $category->id])); ?>"
                                   class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition duration-150 ease-in-out">
                                    استكشف المحتوى
                                </a>
                                
                                <div class="grid grid-cols-3 gap-2">
                                    <a href="<?php echo e(route('search', ['category' => $category->id, 'type' => 'lessons'])); ?>"
                                       class="text-center px-2 py-1 text-xs border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition duration-150 ease-in-out">
                                        الدروس
                                    </a>
                                    <a href="<?php echo e(route('search', ['category' => $category->id, 'type' => 'documentation'])); ?>"
                                       class="text-center px-2 py-1 text-xs border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition duration-150 ease-in-out">
                                        الوثائق
                                    </a>
                                    <a href="<?php echo e(route('search', ['category' => $category->id, 'type' => 'exercises'])); ?>"
                                       class="text-center px-2 py-1 text-xs border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition duration-150 ease-in-out">
                                        التمارين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-16">
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">لا توجد فئات متاحة</h3>
                <p class="mt-2 text-gray-500">لم يتم إنشاء أي فئات بعد.</p>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">لا تجد ما تبحث عنه؟</h2>
                <p class="text-gray-600 mb-6">استخدم البحث للعثور على محتوى محدد أو تصفح جميع المواد المتاحة</p>
                <div class="flex justify-center space-x-4">
                    <a href="<?php echo e(route('search')); ?>" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition duration-150 ease-in-out">
                        البحث في المحتوى
                    </a>
                    <a href="<?php echo e(route('search', ['type' => 'lessons'])); ?>" class="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 transition duration-150 ease-in-out">
                        تصفح جميع الدروس
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\website\taalim-academy\resources\views/categories/index.blade.php ENDPATH**/ ?>