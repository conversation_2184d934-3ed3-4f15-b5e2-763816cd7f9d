<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $users = User::all();

        if ($categories->isEmpty() || $users->isEmpty()) {
            $this->command->info('Please run CategorySeeder and UserSeeder first.');
            return;
        }

        $courses = [
            [
                'title_ar' => 'دورة JavaScript الشاملة',
                'title_en' => 'Complete JavaScript Course',
                'description_ar' => 'تعلم أساسيات وتقنيات JavaScript المتقدمة من الصفر حتى الاحتراف',
                'description_en' => 'Learn JavaScript fundamentals and advanced techniques from zero to mastery',
                'content_ar' => 'محتوى الدورة يشمل المتغيرات، الدوال، الكائنات، والبرمجة غير المتزامنة',
                'content_en' => 'Course content includes variables, functions, objects, and asynchronous programming',
                'difficulty_level' => 'beginner',
                'duration_hours' => 25,
                'price' => 0,
                'is_free' => true,
                'is_published' => true,
                'tags' => ['javascript', 'programming', 'web-development'],
            ],
            [
                'title_ar' => 'تطوير تطبيقات الويب بـ Python',
                'title_en' => 'Python Web Development',
                'description_ar' => 'تعلم بناء تطبيقات الويب الحديثة باستخدام Django و Flask',
                'description_en' => 'Learn to build modern web applications using Django and Flask',
                'content_ar' => 'الدورة تغطي Django، Flask، قواعد البيانات، والنشر',
                'content_en' => 'Course covers Django, Flask, databases, and deployment',
                'difficulty_level' => 'intermediate',
                'duration_hours' => 40,
                'price' => 299.00,
                'is_free' => false,
                'is_published' => true,
                'tags' => ['python', 'django', 'flask', 'web-development'],
            ],
            [
                'title_ar' => 'إتقان React و Next.js',
                'title_en' => 'Mastering React & Next.js',
                'description_ar' => 'بناء تطبيقات ويب حديثة وسريعة باستخدام React و Next.js',
                'description_en' => 'Build modern and fast web applications using React and Next.js',
                'content_ar' => 'تعلم React Hooks، State Management، SSR، والتحسين',
                'content_en' => 'Learn React Hooks, State Management, SSR, and optimization',
                'difficulty_level' => 'advanced',
                'duration_hours' => 35,
                'price' => 499.00,
                'is_free' => false,
                'is_published' => true,
                'tags' => ['react', 'nextjs', 'frontend', 'javascript'],
            ],
            [
                'title_ar' => 'أساسيات تصميم UI/UX',
                'title_en' => 'UI/UX Design Fundamentals',
                'description_ar' => 'تعلم مبادئ تصميم واجهات المستخدم وتجربة المستخدم',
                'description_en' => 'Learn user interface and user experience design principles',
                'content_ar' => 'الدورة تشمل Figma، Adobe XD، وأساسيات التصميم',
                'content_en' => 'Course includes Figma, Adobe XD, and design fundamentals',
                'difficulty_level' => 'beginner',
                'duration_hours' => 20,
                'price' => 0,
                'is_free' => true,
                'is_published' => true,
                'tags' => ['ui', 'ux', 'design', 'figma'],
            ],
            [
                'title_ar' => 'DevOps و Cloud Computing',
                'title_en' => 'DevOps & Cloud Computing',
                'description_ar' => 'تعلم أدوات وممارسات DevOps والحوسبة السحابية',
                'description_en' => 'Learn DevOps tools and practices and cloud computing',
                'content_ar' => 'Docker، Kubernetes، AWS، CI/CD، والمراقبة',
                'content_en' => 'Docker, Kubernetes, AWS, CI/CD, and monitoring',
                'difficulty_level' => 'advanced',
                'duration_hours' => 45,
                'price' => 399.00,
                'is_free' => false,
                'is_published' => true,
                'tags' => ['devops', 'docker', 'kubernetes', 'aws'],
            ],
            [
                'title_ar' => 'علم البيانات والذكاء الاصطناعي',
                'title_en' => 'Data Science & AI',
                'description_ar' => 'تعلم تحليل البيانات والتعلم الآلي باستخدام Python',
                'description_en' => 'Learn data analysis and machine learning using Python',
                'content_ar' => 'Pandas، NumPy، Scikit-learn، TensorFlow، والتصور',
                'content_en' => 'Pandas, NumPy, Scikit-learn, TensorFlow, and visualization',
                'difficulty_level' => 'intermediate',
                'duration_hours' => 60,
                'price' => 599.00,
                'is_free' => false,
                'is_published' => true,
                'tags' => ['data-science', 'ai', 'machine-learning', 'python'],
            ],
        ];

        foreach ($courses as $courseData) {
            Course::create([
                ...$courseData,
                'category_id' => $categories->random()->id,
                'user_id' => $users->random()->id,
                'sort_order' => rand(1, 100),
            ]);
        }

        $this->command->info('Sample courses created successfully!');
    }
}
