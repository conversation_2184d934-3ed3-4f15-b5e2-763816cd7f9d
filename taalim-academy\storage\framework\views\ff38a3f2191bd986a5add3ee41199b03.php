<?php $__env->startSection('header'); ?>
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        لوحة التحكم الطلابية
    </h2>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Welcome Card -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6 text-gray-900">
                <h3 class="text-lg font-semibold mb-2">مرحباً بك <?php echo e(Auth::user()->name); ?>!</h3>
                <p class="text-gray-600">
                    استمر في رحلة تعلم البرمجة واكتشف المزيد من الدروس والتمارين المثيرة.
                </p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">الدروس المكتملة</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['completed_lessons']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">قيد التعلم</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['in_progress_lessons']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">الدروس المحفوظة</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['saved_lessons']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">التمارين المحلولة</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['completed_exercises']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">النقاط المكتسبة</div>
                            <div class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_points']); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Recommendations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Lessons -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">النشاط الأخير</h3>
                    <div class="space-y-3">
                        <?php $__empty_1 = true; $__currentLoopData = $recent_lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $progress): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($progress->lesson->title_ar); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e($progress->lesson->category->name_ar ?? 'غير محدد'); ?> - 
                                        <?php if($progress->status === 'completed'): ?>
                                            <span class="text-green-600">مكتمل</span>
                                        <?php elseif($progress->status === 'in_progress'): ?>
                                            <span class="text-blue-600">قيد التعلم</span>
                                        <?php else: ?>
                                            <span class="text-gray-600">لم يبدأ</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400">
                                    <?php echo e($progress->updated_at->diffForHumans()); ?>

                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <p class="text-gray-500 text-sm">لا يوجد نشاط حديث</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recommended Lessons -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">دروس مقترحة لك</h3>
                    <div class="space-y-3">
                        <?php $__empty_1 = true; $__currentLoopData = $recommended_lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($lesson->title_ar); ?></div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e($lesson->category->name_ar ?? 'غير محدد'); ?> - 
                                        <?php if($lesson->difficulty_level === 'beginner'): ?>
                                            <span class="text-green-600">مبتدئ</span>
                                        <?php elseif($lesson->difficulty_level === 'intermediate'): ?>
                                            <span class="text-yellow-600">متوسط</span>
                                        <?php else: ?>
                                            <span class="text-red-600">متقدم</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">ابدأ التعلم</a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <p class="text-gray-500 text-sm">لا توجد دروس مقترحة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">تصفح الدروس</h4>
                                <p class="text-sm text-gray-500">اكتشف دروس جديدة</p>
                            </div>
                        </div>
                    </a>

                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">حل التمارين</h4>
                                <p class="text-sm text-gray-500">اختبر مهاراتك</p>
                            </div>
                        </div>
                    </a>

                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">الدروس المحفوظة</h4>
                                <p class="text-sm text-gray-500">راجع دروسك المفضلة</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\website\taalim-academy\resources\views/student/dashboard.blade.php ENDPATH**/ ?>