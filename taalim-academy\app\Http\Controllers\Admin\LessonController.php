<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Helpers\MarkdownProcessor;

class LessonController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $lessons = Lesson::with(['category', 'user'])
            ->latest()
            ->paginate(15);

        return view('admin.lessons.index', compact('lessons'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('admin.lessons.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'content_ar' => 'required|string',
            'content_en' => 'nullable|string',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'estimated_duration' => 'nullable|integer|min:1',
            'featured_image' => 'nullable|string',
            'video_urls' => 'nullable|array',
            'video_urls.*' => 'url',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['slug'] = Str::slug($validated['title_ar']);
        $validated['published_at'] = $validated['is_published'] ? now() : null;

        Lesson::create($validated);

        return redirect()->route('admin.lessons.index')
            ->with('success', 'تم إنشاء الدرس بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Lesson $lesson)
    {
        $lesson->load(['category', 'user']);

        // Convert Markdown to HTML with syntax highlighting
        $processor = new MarkdownProcessor();
        $lesson->content_ar_html = $processor->toHtml($lesson->content_ar);
        if ($lesson->content_en) {
            $lesson->content_en_html = $processor->toHtml($lesson->content_en);
        }

        return view('admin.lessons.show', compact('lesson'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Lesson $lesson)
    {
        $categories = Category::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('admin.lessons.edit', compact('lesson', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lesson $lesson)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'content_ar' => 'required|string',
            'content_en' => 'nullable|string',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'estimated_duration' => 'nullable|integer|min:1',
            'featured_image' => 'nullable|string',
            'video_urls' => 'nullable|array',
            'video_urls.*' => 'url',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $validated['slug'] = Str::slug($validated['title_ar']);

        // Update published_at only if changing from unpublished to published
        if ($validated['is_published'] && !$lesson->is_published) {
            $validated['published_at'] = now();
        } elseif (!$validated['is_published']) {
            $validated['published_at'] = null;
        }

        $lesson->update($validated);

        return redirect()->route('admin.lessons.index')
            ->with('success', 'تم تحديث الدرس بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lesson $lesson)
    {
        $lesson->delete();

        return redirect()->route('admin.lessons.index')
            ->with('success', 'تم حذف الدرس بنجاح');
    }
}
