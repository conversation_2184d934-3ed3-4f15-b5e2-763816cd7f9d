<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exercise;
use App\Models\Category;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class ExerciseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Exercise::with(['category', 'lesson', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title_ar', 'LIKE', "%{$search}%")
                  ->orWhere('title_en', 'LIKE', "%{$search}%")
                  ->orWhere('description_ar', 'LIKE', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->get('category'));
        }

        // Type filter
        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        // Difficulty filter
        if ($request->filled('difficulty')) {
            $query->where('difficulty_level', $request->get('difficulty'));
        }

        $exercises = $query->latest()->paginate(15);
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();

        return view('admin.exercises.index', compact('exercises', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();
        $lessons = Lesson::where('is_published', true)->orderBy('title_ar')->get();

        return view('admin.exercises.create', compact('categories', 'lessons'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'title_ar' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'instructions_ar' => 'required|string',
            'type' => 'required|in:quiz,coding,mixed',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'questions' => 'nullable|array',
            'starter_code' => 'nullable|string',
            'solution_code' => 'nullable|string',
            'test_cases' => 'nullable|array',
            'max_attempts' => 'nullable|integer|min:1',
            'time_limit' => 'nullable|integer|min:1',
            'points' => 'required|integer|min:1',
            'is_published' => 'boolean',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['slug'] = Str::slug($validated['title_ar']);
        $validated['published_at'] = $validated['is_published'] ? now() : null;

        Exercise::create($validated);

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم إنشاء التمرين بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Exercise $exercise)
    {
        $exercise->load(['category', 'lesson', 'user', 'submissions']);
        return view('admin.exercises.show', compact('exercise'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Exercise $exercise)
    {
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();
        $lessons = Lesson::where('is_published', true)->orderBy('title_ar')->get();

        return view('admin.exercises.edit', compact('exercise', 'categories', 'lessons'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Exercise $exercise)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'title_ar' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'instructions_ar' => 'required|string',
            'type' => 'required|in:quiz,coding,mixed',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'questions' => 'nullable|array',
            'starter_code' => 'nullable|string',
            'solution_code' => 'nullable|string',
            'test_cases' => 'nullable|array',
            'max_attempts' => 'nullable|integer|min:1',
            'time_limit' => 'nullable|integer|min:1',
            'points' => 'required|integer|min:1',
            'is_published' => 'boolean',
        ]);

        $exercise->update($validated);

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم تحديث التمرين بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Exercise $exercise)
    {
        $exercise->delete();

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم حذف التمرين بنجاح');
    }
}
