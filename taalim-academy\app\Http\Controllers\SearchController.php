<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\Documentation;
use App\Models\Exercise;
use App\Models\Category;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        $query = $request->get('q');
        $category = $request->get('category');
        $type = $request->get('type', 'all'); // all, lessons, documentation, exercises

        $results = collect();
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();

        if ($query) {
            // Search lessons
            if ($type === 'all' || $type === 'lessons') {
                $lessons = Lesson::where('is_published', true)
                    ->where(function($q) use ($query) {
                        $q->where('title_ar', 'LIKE', "%{$query}%")
                          ->orWhere('title_en', 'LIKE', "%{$query}%")
                          ->orWhere('description_ar', 'LIKE', "%{$query}%")
                          ->orWhere('content_ar', 'LIKE', "%{$query}%");
                    })
                    ->when($category, function($q) use ($category) {
                        $q->where('category_id', $category);
                    })
                    ->with('category')
                    ->get()
                    ->map(function($lesson) {
                        $lesson->search_type = 'lesson';
                        return $lesson;
                    });

                $results = $results->merge($lessons);
            }

            // Search documentation
            if ($type === 'all' || $type === 'documentation') {
                $documentation = Documentation::where('is_published', true)
                    ->where(function($q) use ($query) {
                        $q->where('title_ar', 'LIKE', "%{$query}%")
                          ->orWhere('title_en', 'LIKE', "%{$query}%")
                          ->orWhere('description_ar', 'LIKE', "%{$query}%")
                          ->orWhere('content_ar', 'LIKE', "%{$query}%");
                    })
                    ->when($category, function($q) use ($category) {
                        $q->where('category_id', $category);
                    })
                    ->with('category')
                    ->get()
                    ->map(function($doc) {
                        $doc->search_type = 'documentation';
                        return $doc;
                    });

                $results = $results->merge($documentation);
            }

            // Search exercises
            if ($type === 'all' || $type === 'exercises') {
                $exercises = Exercise::where('is_published', true)
                    ->where(function($q) use ($query) {
                        $q->where('title_ar', 'LIKE', "%{$query}%")
                          ->orWhere('title_en', 'LIKE', "%{$query}%")
                          ->orWhere('description_ar', 'LIKE', "%{$query}%")
                          ->orWhere('instructions_ar', 'LIKE', "%{$query}%");
                    })
                    ->when($category, function($q) use ($category) {
                        $q->where('category_id', $category);
                    })
                    ->with('category')
                    ->get()
                    ->map(function($exercise) {
                        $exercise->search_type = 'exercise';
                        return $exercise;
                    });

                $results = $results->merge($exercises);
            }
        }

        return view('search.index', compact('results', 'categories', 'query', 'category', 'type'));
    }
}
