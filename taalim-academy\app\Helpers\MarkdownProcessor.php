<?php

namespace App\Helpers;

use League\CommonMark\CommonMarkConverter;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;

class MarkdownProcessor
{
    private $converter;

    public function __construct()
    {
        $environment = new Environment();
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        $this->converter = new CommonMarkConverter([], $environment);
    }

    /**
     * Convert Markdown to HTML with syntax highlighting support
     */
    public function toHtml(string $markdown): string
    {
        $html = $this->converter->convert($markdown)->getContent();

        // Process code blocks for syntax highlighting
        $html = $this->processCodeBlocks($html);

        return $html;
    }

    /**
     * Process code blocks to add Prism.js classes
     */
    private function processCodeBlocks(string $html): string
    {
        // Pattern to match code blocks with language specification
        $pattern = '/<pre><code class="language-([^"]+)">(.*?)<\/code><\/pre>/s';

        $html = preg_replace_callback($pattern, function($matches) {
            $language = $matches[1];
            $code = $matches[2];

            // Map common language aliases
            $languageMap = [
                'js' => 'javascript',
                'ts' => 'typescript',
                'py' => 'python',
                'rb' => 'ruby',
                'sh' => 'bash',
                'yml' => 'yaml',
            ];

            $prismLanguage = $languageMap[$language] ?? $language;

            return sprintf(
                '<div class="code-block"><div class="code-block-header">%s</div><pre class="language-%s line-numbers"><code class="language-%s">%s</code></pre></div>',
                ucfirst($language),
                $prismLanguage,
                $prismLanguage,
                $code
            );
        }, $html);

        // Pattern to match code blocks without language specification
        $pattern = '/<pre><code>(.*?)<\/code><\/pre>/s';

        $html = preg_replace_callback($pattern, function($matches) {
            $code = $matches[1];

            return sprintf(
                '<div class="code-block"><div class="code-block-header">Code</div><pre class="language-none line-numbers"><code>%s</code></pre></div>',
                $code
            );
        }, $html);

        return $html;
    }

    /**
     * Extract code snippets from markdown for syntax validation
     */
    public function extractCodeSnippets(string $markdown): array
    {
        $snippets = [];

        // Pattern to match fenced code blocks
        $pattern = '/```(\w+)?\n(.*?)\n```/s';

        preg_match_all($pattern, $markdown, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $snippets[] = [
                'language' => $match[1] ?? 'text',
                'code' => trim($match[2]),
                'full_match' => $match[0]
            ];
        }

        return $snippets;
    }

    /**
     * Validate code syntax (basic validation)
     */
    public function validateCodeSyntax(string $code, string $language): array
    {
        $errors = [];

        switch (strtolower($language)) {
            case 'php':
                // Basic PHP syntax validation
                if (strpos($code, '<?php') === false && strpos($code, '<?=') === false) {
                    $errors[] = 'PHP code should start with <?php tag';
                }
                break;

            case 'javascript':
            case 'js':
                // Basic JavaScript validation
                if (substr_count($code, '{') !== substr_count($code, '}')) {
                    $errors[] = 'Mismatched curly braces in JavaScript code';
                }
                break;

            case 'python':
            case 'py':
                // Basic Python validation
                $lines = explode("\n", $code);
                foreach ($lines as $line) {
                    if (preg_match('/^\s*\t\s*/', $line)) {
                        $errors[] = 'Mixed tabs and spaces in Python code';
                        break;
                    }
                }
                break;
        }

        return $errors;
    }
}
