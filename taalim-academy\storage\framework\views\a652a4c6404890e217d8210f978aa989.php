<?php $__env->startSection('header'); ?>
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        البحث في المحتوى
    </h2>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Search Form -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('search')); ?>" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search Query -->
                        <div class="md:col-span-2">
                            <label for="q" class="block text-sm font-medium text-gray-700">البحث</label>
                            <input type="text" name="q" id="q" value="<?php echo e($query); ?>" 
                                   placeholder="ابحث في الدروس والوثائق والتمارين..."
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <!-- Category Filter -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700">الفئة</label>
                            <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">جميع الفئات</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($cat->id); ?>" <?php echo e($category == $cat->id ? 'selected' : ''); ?>>
                                        <?php echo e($cat->name_ar); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Type Filter -->
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700">النوع</label>
                            <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="all" <?php echo e($type === 'all' ? 'selected' : ''); ?>>الكل</option>
                                <option value="lessons" <?php echo e($type === 'lessons' ? 'selected' : ''); ?>>الدروس</option>
                                <option value="documentation" <?php echo e($type === 'documentation' ? 'selected' : ''); ?>>الوثائق</option>
                                <option value="exercises" <?php echo e($type === 'exercises' ? 'selected' : ''); ?>>التمارين</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Results -->
        <?php if($query): ?>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            نتائج البحث عن "<?php echo e($query); ?>"
                        </h3>
                        <p class="text-sm text-gray-600">
                            تم العثور على <?php echo e($results->count()); ?> نتيجة
                        </p>
                    </div>

                    <?php if($results->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition duration-150 ease-in-out">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <!-- Type Badge -->
                                                <?php if($result->search_type === 'lesson'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        درس
                                                    </span>
                                                <?php elseif($result->search_type === 'documentation'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        وثيقة
                                                    </span>
                                                <?php elseif($result->search_type === 'exercise'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                        تمرين
                                                    </span>
                                                <?php endif; ?>

                                                <!-- Category Badge -->
                                                <?php if($result->category): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <?php echo e($result->category->name_ar); ?>

                                                    </span>
                                                <?php endif; ?>

                                                <!-- Difficulty Badge (for lessons and exercises) -->
                                                <?php if(isset($result->difficulty_level)): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                        <?php if($result->difficulty_level === 'beginner'): ?> bg-green-100 text-green-800
                                                        <?php elseif($result->difficulty_level === 'intermediate'): ?> bg-yellow-100 text-yellow-800
                                                        <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                                        <?php if($result->difficulty_level === 'beginner'): ?> مبتدئ
                                                        <?php elseif($result->difficulty_level === 'intermediate'): ?> متوسط
                                                        <?php else: ?> متقدم <?php endif; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <h4 class="text-lg font-medium text-gray-900 mb-2">
                                                <?php echo e($result->title_ar); ?>

                                            </h4>

                                            <?php if($result->description_ar): ?>
                                                <p class="text-gray-600 text-sm mb-2">
                                                    <?php echo e(Str::limit($result->description_ar, 150)); ?>

                                                </p>
                                            <?php endif; ?>

                                            <div class="text-xs text-gray-500">
                                                آخر تحديث: <?php echo e($result->updated_at->diffForHumans()); ?>

                                            </div>
                                        </div>

                                        <div class="mr-4">
                                            <a href="#" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد نتائج</h3>
                            <p class="mt-1 text-sm text-gray-500">لم نتمكن من العثور على أي نتائج تطابق بحثك.</p>
                            <div class="mt-6">
                                <p class="text-sm text-gray-500">جرب:</p>
                                <ul class="mt-2 text-sm text-gray-500 list-disc list-inside">
                                    <li>التحقق من الإملاء</li>
                                    <li>استخدام كلمات مختلفة</li>
                                    <li>استخدام كلمات أكثر عمومية</li>
                                    <li>تغيير فلاتر البحث</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900">ابحث في المحتوى</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        ابحث في الدروس والوثائق والتمارين للعثور على ما تحتاجه
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\website\taalim-academy\resources\views/search/index.blade.php ENDPATH**/ ?>