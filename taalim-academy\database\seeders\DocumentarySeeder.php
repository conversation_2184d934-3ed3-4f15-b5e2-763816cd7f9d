<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Documentary;
use App\Models\Category;
use App\Models\User;

class DocumentarySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $users = User::all();

        if ($categories->isEmpty() || $users->isEmpty()) {
            $this->command->info('Please run CategorySeeder and UserSeeder first.');
            return;
        }

        $documentaries = [
            [
                'title_ar' => 'ثورة الذكاء الاصطناعي',
                'title_en' => 'The AI Revolution',
                'description_ar' => 'رحلة استكشافية في عالم الذكاء الاصطناعي وتأثيره على مستقبل البشرية',
                'description_en' => 'An exploratory journey into the world of AI and its impact on humanity\'s future',
                'video_url' => 'https://example.com/ai-revolution.mp4',
                'duration_minutes' => 120,
                'director' => 'أحمد محمد',
                'release_year' => 2024,
                'quality' => '4K',
                'is_published' => true,
                'views_count' => 2100000,
                'rating' => 4.9,
                'tags' => ['ai', 'technology', 'future'],
            ],
            [
                'title_ar' => 'رحلة في عالم البرمجة',
                'title_en' => 'Journey into Programming',
                'description_ar' => 'استكشاف تاريخ البرمجة وتطورها عبر العقود',
                'description_en' => 'Exploring the history and evolution of programming through the decades',
                'video_url' => 'https://example.com/programming-journey.mp4',
                'duration_minutes' => 85,
                'director' => 'سارة أحمد',
                'release_year' => 2023,
                'quality' => '1080p',
                'is_published' => true,
                'views_count' => 1200000,
                'rating' => 4.7,
                'tags' => ['programming', 'history', 'technology'],
            ],
            [
                'title_ar' => 'حروب الإنترنت الخفية',
                'title_en' => 'Hidden Internet Wars',
                'description_ar' => 'كشف أسرار الأمن السيبراني والهجمات الإلكترونية',
                'description_en' => 'Revealing the secrets of cybersecurity and cyber attacks',
                'video_url' => 'https://example.com/cyber-wars.mp4',
                'duration_minutes' => 110,
                'director' => 'محمد علي',
                'release_year' => 2023,
                'quality' => '1080p',
                'is_published' => true,
                'views_count' => 890000,
                'rating' => 4.6,
                'tags' => ['cybersecurity', 'hacking', 'internet'],
            ],
            [
                'title_ar' => 'التكنولوجيا في الفضاء',
                'title_en' => 'Technology in Space',
                'description_ar' => 'كيف تطورت التكنولوجيا لاستكشاف الفضاء الخارجي',
                'description_en' => 'How technology evolved to explore outer space',
                'video_url' => 'https://example.com/space-tech.mp4',
                'duration_minutes' => 75,
                'director' => 'ليلى حسن',
                'release_year' => 2024,
                'quality' => '4K',
                'is_published' => true,
                'views_count' => 1500000,
                'rating' => 4.8,
                'tags' => ['space', 'technology', 'exploration'],
            ],
            [
                'title_ar' => 'عمالقة وادي السيليكون',
                'title_en' => 'Silicon Valley Giants',
                'description_ar' => 'قصص نجاح أكبر الشركات التقنية في العالم',
                'description_en' => 'Success stories of the world\'s biggest tech companies',
                'video_url' => 'https://example.com/silicon-valley.mp4',
                'duration_minutes' => 90,
                'director' => 'عمر خالد',
                'release_year' => 2023,
                'quality' => '1080p',
                'is_published' => true,
                'views_count' => 3200000,
                'rating' => 4.5,
                'tags' => ['silicon-valley', 'companies', 'business'],
            ],
            [
                'title_ar' => 'ثورة البلوك تشين',
                'title_en' => 'Blockchain Revolution',
                'description_ar' => 'كيف تغير تقنية البلوك تشين مستقبل المال والأعمال',
                'description_en' => 'How blockchain technology is changing the future of money and business',
                'video_url' => 'https://example.com/blockchain.mp4',
                'duration_minutes' => 105,
                'director' => 'د. فاطمة النور',
                'release_year' => 2024,
                'quality' => '4K',
                'is_published' => true,
                'views_count' => 750000,
                'rating' => 4.4,
                'tags' => ['blockchain', 'cryptocurrency', 'finance'],
            ],
        ];

        foreach ($documentaries as $documentaryData) {
            Documentary::create([
                ...$documentaryData,
                'category_id' => $categories->random()->id,
                'user_id' => $users->random()->id,
            ]);
        }

        $this->command->info('Sample documentaries created successfully!');
    }
}
