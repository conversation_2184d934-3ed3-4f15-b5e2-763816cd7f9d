<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\UserProgress;
use App\Models\SavedLesson;
use App\Models\Submission;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get user statistics
        $stats = [
            'completed_lessons' => UserProgress::where('user_id', $user->id)
                ->where('status', 'completed')
                ->count(),
            'in_progress_lessons' => UserProgress::where('user_id', $user->id)
                ->where('status', 'in_progress')
                ->count(),
            'saved_lessons' => SavedLesson::where('user_id', $user->id)->count(),
            'completed_exercises' => Submission::where('user_id', $user->id)
                ->where('status', 'correct')
                ->distinct('exercise_id')
                ->count(),
            'total_points' => Submission::where('user_id', $user->id)
                ->where('status', 'correct')
                ->sum('score'),
        ];

        // Get recent activity
        $recent_lessons = UserProgress::where('user_id', $user->id)
            ->with('lesson.category')
            ->latest()
            ->take(5)
            ->get();

        $recent_submissions = Submission::where('user_id', $user->id)
            ->with('exercise.category')
            ->latest()
            ->take(5)
            ->get();

        // Get recommended lessons
        $recommended_lessons = Lesson::where('is_published', true)
            ->whereNotIn('id', function($query) use ($user) {
                $query->select('lesson_id')
                    ->from('user_progress')
                    ->where('user_id', $user->id)
                    ->where('status', 'completed');
            })
            ->with('category')
            ->take(6)
            ->get();

        return view('student.dashboard', compact('stats', 'recent_lessons', 'recent_submissions', 'recommended_lessons'));
    }
}
