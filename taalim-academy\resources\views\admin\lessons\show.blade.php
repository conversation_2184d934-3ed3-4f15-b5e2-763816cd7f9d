@extends('admin.layout')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $lesson->title_ar }}</h1>
            @if($lesson->title_en)
                <p class="text-gray-600">{{ $lesson->title_en }}</p>
            @endif
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.lessons.edit', $lesson) }}" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 focus:bg-yellow-700 active:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                تعديل
            </a>
            <a href="{{ route('admin.lessons.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Lesson Info -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الدرس</h3>
                    <dl class="space-y-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">الفئة</dt>
                            <dd class="text-sm text-gray-900">{{ $lesson->category->name_ar }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">المؤلف</dt>
                            <dd class="text-sm text-gray-900">{{ $lesson->user->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">مستوى الصعوبة</dt>
                            <dd class="text-sm text-gray-900">
                                @if($lesson->difficulty_level === 'beginner') مبتدئ
                                @elseif($lesson->difficulty_level === 'intermediate') متوسط
                                @else متقدم @endif
                            </dd>
                        </div>
                        @if($lesson->estimated_duration)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">المدة المقدرة</dt>
                                <dd class="text-sm text-gray-900">{{ $lesson->estimated_duration }} دقيقة</dd>
                            </div>
                        @endif
                    </dl>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">الحالة</h3>
                    <dl class="space-y-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">حالة النشر</dt>
                            <dd class="text-sm">
                                @if($lesson->is_published)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        منشور
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        مسودة
                                    </span>
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">درس مميز</dt>
                            <dd class="text-sm text-gray-900">{{ $lesson->is_featured ? 'نعم' : 'لا' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">عدد المشاهدات</dt>
                            <dd class="text-sm text-gray-900">{{ $lesson->views_count }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">تاريخ الإنشاء</dt>
                            <dd class="text-sm text-gray-900">{{ $lesson->created_at->format('Y-m-d H:i') }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Description -->
    @if($lesson->description_ar)
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">الوصف</h3>
                <div class="prose max-w-none">
                    <p class="text-gray-700">{{ $lesson->description_ar }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Video URLs -->
    @if($lesson->video_urls && count($lesson->video_urls) > 0)
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">روابط الفيديو</h3>
                <div class="space-y-2">
                    @foreach($lesson->video_urls as $index => $url)
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">{{ $index + 1 }}.</span>
                            <a href="{{ $url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                {{ $url }}
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Lesson Content -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">محتوى الدرس</h3>
            <div class="prose max-w-none">
                {!! $lesson->content_ar_html !!}
            </div>
        </div>
    </div>

    <!-- English Content (if available) -->
    @if($lesson->content_en && isset($lesson->content_en_html))
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">المحتوى الإنجليزي</h3>
                <div class="prose max-w-none">
                    {!! $lesson->content_en_html !!}
                </div>
            </div>
        </div>
    @endif
</div>

<script>
// Re-highlight code blocks after content is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
    }
});
</script>
@endsection
