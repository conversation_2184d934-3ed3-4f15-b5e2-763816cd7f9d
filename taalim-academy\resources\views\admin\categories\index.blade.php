@extends('admin.layout')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50">
    <!-- Modern Header with Floating Card Design -->
    <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 mb-8">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">إدارة الفئات التعليمية</h1>
                    <p class="text-gray-500 mt-1 font-medium">إدارة وتنظيم فئات المحتوى التعليمي بطريقة ذكية</p>
                </div>
            </div>
            <a href="{{ route('admin.categories.create') }}" class="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 ease-out inline-flex items-center">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span>إضافة فئة جديدة</span>
                </div>
                <div class="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </a>
        </div>
    </div>

    <!-- Modern Categories Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Categories Section -->
        <div class="lg:col-span-2">
            @if(isset($categories) && $categories->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($categories as $category)
                        <div class="group bg-white rounded-3xl shadow-lg border border-gray-100 p-8 hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-500 ease-out overflow-hidden relative">
                            <!-- Decorative Background Pattern -->
                            <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-100 to-transparent rounded-full transform translate-x-16 -translate-y-16 opacity-50"></div>

                            <!-- Category Header -->
                            <div class="relative z-10 flex items-start justify-between mb-6">
                                <div class="flex items-center space-x-4">
                                    @if($category->icon)
                                        <div class="w-16 h-16 bg-gradient-to-r from-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-400 to-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <i class="{{ $category->icon }} text-2xl text-white"></i>
                                        </div>
                                    @else
                                        <div class="w-16 h-16 bg-gradient-to-r from-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-400 to-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <span class="text-white font-bold text-xl">{{ substr($category->name_ar, 0, 1) }}</span>
                                        </div>
                                    @endif
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800 mb-1">{{ $category->name_ar }}</h3>
                                        @if($category->name_en)
                                            <p class="text-sm text-gray-500 font-medium">{{ $category->name_en }}</p>
                                        @endif
                                    </div>
                                </div>
                                <!-- Status Badge -->
                                @if($category->is_active)
                                    <div class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-semibold shadow-sm">
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                            <span>نشط</span>
                                        </div>
                                    </div>
                                @else
                                    <div class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-semibold shadow-sm">
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                            <span>غير نشط</span>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Description -->
                            @if($category->description_ar)
                                <p class="text-gray-600 mb-6 leading-relaxed">{{ Str::limit($category->description_ar, 120) }}</p>
                            @endif

                            <!-- Stats Row -->
                            <div class="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-2xl">
                                <div class="text-center">
                                    <div class="text-sm text-gray-500 mb-1">الترتيب</div>
                                    <div class="text-lg font-bold text-gray-800">{{ $category->sort_order }}</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm text-gray-500 mb-1">المحتوى</div>
                                    <div class="text-lg font-bold text-{{ $category->color === '#3B82F6' ? 'blue' : ($category->color === '#F59E0B' ? 'yellow' : 'purple') }}-600">
                                        {{ ($category->lessons_count ?? 0) + ($category->documentation_count ?? 0) + ($category->exercises_count ?? 0) }}
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm text-gray-500 mb-1">الحالة</div>
                                    <div class="text-lg">
                                        @if($category->is_active)
                                            <span class="text-green-500">✓</span>
                                        @else
                                            <span class="text-gray-400">○</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-3">
                                <a href="{{ route('admin.categories.show', $category) }}" class="flex-1 px-4 py-3 bg-blue-50 text-blue-600 rounded-xl font-semibold text-center hover:bg-blue-100 transition-colors duration-200 group">
                                    <div class="flex items-center justify-center space-x-2">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>عرض</span>
                                    </div>
                                </a>
                                <a href="{{ route('admin.categories.edit', $category) }}" class="flex-1 px-4 py-3 bg-yellow-50 text-yellow-600 rounded-xl font-semibold text-center hover:bg-yellow-100 transition-colors duration-200 group">
                                    <div class="flex items-center justify-center space-x-2">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                        </svg>
                                        <span>تعديل</span>
                                    </div>
                                </a>
                                <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" class="flex-1" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="w-full px-4 py-3 bg-red-50 text-red-600 rounded-xl font-semibold hover:bg-red-100 transition-colors duration-200 group">
                                        <div class="flex items-center justify-center space-x-2">
                                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>حذف</span>
                                        </div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if(method_exists($categories, 'links'))
                    <div class="mt-8 flex justify-center">
                        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-4">
                            {{ $categories->links() }}
                        </div>
                    </div>
                @endif
            @else
                <!-- Modern Empty State -->
                <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-12 text-center">
                    <div class="w-24 h-24 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-3">لا توجد فئات تعليمية بعد</h3>
                    <p class="text-gray-500 mb-8 max-w-md mx-auto">ابدأ بإنشاء فئة جديدة لتنظيم المحتوى التعليمي وجعله أكثر سهولة للطلاب.</p>
                    <a href="{{ route('admin.categories.create') }}" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                        <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        إنشاء أول فئة تعليمية
                    </a>
                </div>
            @endif
        </div>

        <!-- Modern Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats Card -->
            <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">إحصائيات سريعة</h3>
                    <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                        </svg>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-blue-50 rounded-2xl">
                        <div>
                            <div class="text-sm text-blue-600 font-medium">إجمالي الفئات</div>
                            <div class="text-2xl font-bold text-blue-700">{{ isset($categories) ? $categories->total() : 0 }}</div>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-4 bg-green-50 rounded-2xl">
                        <div>
                            <div class="text-sm text-green-600 font-medium">الفئات النشطة</div>
                            <div class="text-2xl font-bold text-green-700">{{ isset($categories) ? $categories->where('is_active', true)->count() : 0 }}</div>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reminders Card -->
            <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">تذكيرات إدارية</h3>
                    <div class="w-10 h-10 bg-gradient-to-r from-pink-400 to-red-500 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-start space-x-3 p-3 bg-yellow-50 rounded-xl border-r-4 border-yellow-400">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <div class="text-sm font-medium text-yellow-800">مراجعة الفئات</div>
                            <div class="text-xs text-yellow-600">تأكد من تحديث أوصاف الفئات</div>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-blue-50 rounded-xl border-r-4 border-blue-400">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <div class="text-sm font-medium text-blue-800">ترتيب الفئات</div>
                            <div class="text-xs text-blue-600">راجع ترتيب عرض الفئات</div>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-green-50 rounded-xl border-r-4 border-green-400">
                        <div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                            <div class="text-sm font-medium text-green-800">إضافة محتوى</div>
                            <div class="text-xs text-green-600">أضف دروس جديدة للفئات</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-6">إجراءات سريعة</h3>
                <div class="space-y-3">
                    <a href="{{ route('admin.lessons.create') }}" class="flex items-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 group">
                        <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-blue-800">إضافة درس جديد</div>
                            <div class="text-xs text-blue-600">أنشئ محتوى تعليمي</div>
                        </div>
                    </a>
                    <a href="{{ route('admin.exercises.create') }}" class="flex items-center p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-2xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 group">
                        <div class="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold text-purple-800">إضافة تمرين</div>
                            <div class="text-xs text-purple-600">أنشئ تمرين تفاعلي</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
