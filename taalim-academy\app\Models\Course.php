<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Course extends Model
{
    protected $fillable = [
        'title_ar',
        'title_en',
        'description_ar',
        'description_en',
        'content_ar',
        'content_en',
        'image',
        'video_url',
        'difficulty_level',
        'duration_hours',
        'price',
        'is_free',
        'is_published',
        'sort_order',
        'tags',
        'category_id',
        'user_id',
    ];

    protected $casts = [
        'tags' => 'array',
        'is_free' => 'boolean',
        'is_published' => 'boolean',
        'price' => 'decimal:2',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getDifficultyLabelAttribute(): string
    {
        return match($this->difficulty_level) {
            'beginner' => 'مبتدئ',
            'intermediate' => 'متوسط',
            'advanced' => 'متقدم',
            default => 'غير محدد'
        };
    }
}
