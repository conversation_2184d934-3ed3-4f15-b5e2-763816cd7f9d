<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class CodeBlock extends Component
{
    public $language;
    public $title;
    public $code;

    /**
     * Create a new component instance.
     */
    public function __construct($language = 'text', $title = null, $code = '')
    {
        $this->language = $language;
        $this->title = $title ?? ucfirst($language);
        $this->code = $code;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.code-block');
    }
}
