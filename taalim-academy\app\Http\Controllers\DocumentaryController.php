<?php

namespace App\Http\Controllers;

use App\Models\Documentary;
use Illuminate\Http\Request;

class DocumentaryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('documentaries.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Documentary $documentary)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Documentary $documentary)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Documentary $documentary)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Documentary $documentary)
    {
        //
    }
}
