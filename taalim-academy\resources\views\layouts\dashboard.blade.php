<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }} - لوحة التحكم</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <style>
            body {
                font-family: 'Noto Sans Arabic', sans-serif;
            }
            
            /* Custom scrollbar */
            .custom-scrollbar::-webkit-scrollbar {
                width: 6px;
            }
            
            .custom-scrollbar::-webkit-scrollbar-track {
                background: #f1f5f9;
            }
            
            .custom-scrollbar::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }
            
            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        </style>
    </head>
    <body class="font-sans antialiased bg-gray-50">
        <div class="flex h-screen overflow-hidden">
            <!-- Sidebar -->
            <aside class="hidden lg:flex lg:flex-shrink-0">
                <div class="flex flex-col w-72 bg-white border-l border-gray-200 shadow-sm">
                    <!-- Logo -->
                    <div class="flex items-center justify-center h-16 px-6 bg-gradient-to-l from-blue-600 to-blue-700">
                        <h1 class="text-xl font-bold text-white">أكاديمية تعليم</h1>
                    </div>
                    
                    <!-- Navigation -->
                    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto custom-scrollbar">
                        <!-- Dashboard -->
                        <a href="{{ route('dashboard') }}" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors {{ request()->routeIs('dashboard') ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-700 hover:bg-gray-50' }}">
                            <i class="fas fa-tachometer-alt ml-3 w-5 h-5"></i>
                            لوحة التحكم
                        </a>
                        
                        <!-- Content Management -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">إدارة المحتوى</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('categories.index') }}" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors {{ request()->routeIs('categories.*') ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-700 hover:bg-gray-50' }}">
                                    <i class="fas fa-tags ml-3 w-5 h-5"></i>
                                    الفئات
                                </a>
                                
                                <a href="{{ route('courses.index') }}" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors {{ request()->routeIs('courses.*') ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-700 hover:bg-gray-50' }}">
                                    <i class="fas fa-graduation-cap ml-3 w-5 h-5"></i>
                                    الدورات
                                    <span class="mr-auto bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">جديد</span>
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-book-open ml-3 w-5 h-5"></i>
                                    الدروس
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-file-alt ml-3 w-5 h-5"></i>
                                    الوثائق
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-dumbbell ml-3 w-5 h-5"></i>
                                    التمارين
                                </a>
                                
                                <a href="{{ route('documentaries.index') }}" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors {{ request()->routeIs('documentaries.*') ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-700 hover:bg-gray-50' }}">
                                    <i class="fas fa-video ml-3 w-5 h-5"></i>
                                    الأفلام الوثائقية
                                    <span class="mr-auto bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">جديد</span>
                                </a>
                            </div>
                        </div>
                        
                        <!-- Analytics -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">التحليلات</h3>
                            <div class="mt-2 space-y-1">
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-chart-bar ml-3 w-5 h-5"></i>
                                    الإحصائيات
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-users ml-3 w-5 h-5"></i>
                                    المستخدمين
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-star ml-3 w-5 h-5"></i>
                                    التقييمات
                                </a>
                            </div>
                        </div>
                        
                        <!-- Settings -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">الإعدادات</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('profile.edit') }}" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-user-cog ml-3 w-5 h-5"></i>
                                    الملف الشخصي
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-cog ml-3 w-5 h-5"></i>
                                    إعدادات النظام
                                </a>
                            </div>
                        </div>
                    </nav>
                    
                    <!-- User Profile -->
                    <div class="flex-shrink-0 p-4 border-t border-gray-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white font-medium text-sm">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="mr-3 flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ Auth::user()->name }}</p>
                                <p class="text-xs text-gray-500 truncate">{{ Auth::user()->email }}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                                        <i class="fas fa-sign-out-alt w-4 h-4"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Mobile sidebar -->
            <div class="lg:hidden" x-data="{ sidebarOpen: false }">
                <!-- Mobile sidebar overlay -->
                <div x-show="sidebarOpen" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" @click="sidebarOpen = false"></div>
                
                <!-- Mobile sidebar -->
                <div x-show="sidebarOpen" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" x-transition:enter="transform transition ease-in-out duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transform transition ease-in-out duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full">
                    <!-- Mobile sidebar content (same as desktop) -->
                    <div class="flex flex-col h-full">
                        <!-- Logo -->
                        <div class="flex items-center justify-between h-16 px-6 bg-gradient-to-l from-blue-600 to-blue-700">
                            <h1 class="text-xl font-bold text-white">أكاديمية تعليم</h1>
                            <button @click="sidebarOpen = false" class="text-white hover:text-gray-200">
                                <i class="fas fa-times w-6 h-6"></i>
                            </button>
                        </div>
                        
                        <!-- Navigation (same as desktop) -->
                        <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                            <!-- Same navigation content as desktop -->
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top bar -->
                <header class="bg-white shadow-sm border-b border-gray-200 lg:hidden">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button @click="sidebarOpen = true" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-bars w-6 h-6"></i>
                        </button>
                        <h1 class="text-lg font-semibold text-gray-900">أكاديمية تعليم</h1>
                        <div class="w-6"></div>
                    </div>
                </header>

                <!-- Page content -->
                <main class="flex-1 overflow-y-auto custom-scrollbar">
                    @hasSection('content')
                        @yield('content')
                    @else
                        {{ $slot ?? '' }}
                    @endif
                </main>
            </div>
        </div>

        <!-- Alpine.js -->
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    </body>
</html>
