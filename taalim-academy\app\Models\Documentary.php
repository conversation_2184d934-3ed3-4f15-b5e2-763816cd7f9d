<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Documentary extends Model
{
    protected $fillable = [
        'title_ar',
        'title_en',
        'description_ar',
        'description_en',
        'video_url',
        'thumbnail',
        'duration_minutes',
        'director',
        'release_year',
        'quality',
        'is_published',
        'views_count',
        'rating',
        'tags',
        'category_id',
        'user_id',
    ];

    protected $casts = [
        'tags' => 'array',
        'is_published' => 'boolean',
        'rating' => 'decimal:2',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getQualityLabelAttribute(): string
    {
        return match($this->quality) {
            '720p' => 'HD 720p',
            '1080p' => 'Full HD 1080p',
            '4K' => 'Ultra HD 4K',
            default => 'جودة عادية'
        };
    }

    public function getDurationFormattedAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return "{$hours} ساعة و {$minutes} دقيقة";
        }

        return "{$minutes} دقيقة";
    }
}
