<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\LessonController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\DocumentationController;
use App\Http\Controllers\Admin\ExerciseController;
use App\Http\Controllers\Student\DashboardController as StudentDashboardController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\DocumentaryController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    return view('welcome-arabic');
});

Route::get('/dashboard', function () {
    if (Auth::user()->isAdmin()) {
        return redirect()->route('admin.dashboard');
    }
    return app(StudentDashboardController::class)->index();
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Search routes
    Route::get('/search', [SearchController::class, 'index'])->name('search');

    // Public content routes for students
    Route::get('/lessons', function() {
        return redirect()->route('search', ['type' => 'lessons']);
    })->name('lessons.index');

    Route::get('/documentation', function() {
        return redirect()->route('search', ['type' => 'documentation']);
    })->name('documentation.index');

    Route::get('/exercises', function() {
        return redirect()->route('search', ['type' => 'exercises']);
    })->name('exercises.index');

    Route::get('/categories', function() {
        $categories = \App\Models\Category::where('is_active', true)->orderBy('sort_order')->get();
        return view('categories.index', compact('categories'));
    })->name('categories.index');

    // New content routes
    Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
    Route::get('/documentaries', [DocumentaryController::class, 'index'])->name('documentaries.index');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::resource('categories', CategoryController::class);
    Route::resource('lessons', LessonController::class);
    Route::resource('documentation', DocumentationController::class);
    Route::resource('exercises', ExerciseController::class);
    Route::resource('users', UserController::class);
});

require __DIR__.'/auth.php';
