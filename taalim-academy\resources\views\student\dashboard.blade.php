@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        لوحة التحكم الطلابية
    </h2>
@endsection

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <!-- Modern Welcome Card with Live Session Style -->
        <div class="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 mb-8 relative overflow-hidden">
            <!-- Decorative Background -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-100 to-purple-100 rounded-full transform translate-x-32 -translate-y-32 opacity-50"></div>

            <div class="relative z-10 grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <!-- Welcome Message -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-4 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a1 1 0 001 1h3v-8h6v8h3a1 1 0 001-1V7l-7-5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">مرحباً بك {{ Auth::user()->name }}!</h3>
                            <p class="text-gray-600 text-lg font-medium">
                                استمر في رحلة تعلم البرمجة واكتشف المزيد من الدروس والتمارين المثيرة
                            </p>
                        </div>
                    </div>

                    <!-- Quick Action Buttons -->
                    <div class="flex space-x-4">
                        <a href="{{ route('search') }}" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                            ابدأ التعلم الآن
                        </a>
                        <a href="{{ route('categories.index') }}" class="px-6 py-3 bg-white border-2 border-blue-200 text-blue-600 font-semibold rounded-2xl hover:bg-blue-50 transition-all duration-300">
                            تصفح الفئات
                        </a>
                    </div>
                </div>

                <!-- Avatar/Profile Section -->
                <div class="text-center">
                    <div class="w-32 h-32 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-xl">
                        <span class="text-4xl font-bold text-white">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                    <div class="text-sm text-gray-500 font-medium">طالب نشط</div>
                    <div class="flex items-center justify-center mt-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                        <span class="text-green-600 text-sm font-semibold">متصل الآن</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-3xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-semibold text-green-600 mb-1">الدروس المكتملة</div>
                        <div class="text-3xl font-bold text-gray-800">{{ $stats['completed_lessons'] }}</div>
                        <div class="text-xs text-gray-500 mt-1">+2 هذا الأسبوع</div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">قيد التعلم</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $stats['in_progress_lessons'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">الدروس المحفوظة</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $stats['saved_lessons'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">التمارين المحلولة</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $stats['completed_exercises'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="mr-4">
                            <div class="text-sm font-medium text-gray-500">النقاط المكتسبة</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $stats['total_points'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Recommendations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Lessons -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">النشاط الأخير</h3>
                    <div class="space-y-3">
                        @forelse($recent_lessons as $progress)
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $progress->lesson->title_ar }}</div>
                                    <div class="text-sm text-gray-500">
                                        {{ $progress->lesson->category->name_ar ?? 'غير محدد' }} - 
                                        @if($progress->status === 'completed')
                                            <span class="text-green-600">مكتمل</span>
                                        @elseif($progress->status === 'in_progress')
                                            <span class="text-blue-600">قيد التعلم</span>
                                        @else
                                            <span class="text-gray-600">لم يبدأ</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400">
                                    {{ $progress->updated_at->diffForHumans() }}
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-sm">لا يوجد نشاط حديث</p>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Recommended Lessons -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">دروس مقترحة لك</h3>
                    <div class="space-y-3">
                        @forelse($recommended_lessons as $lesson)
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $lesson->title_ar }}</div>
                                    <div class="text-sm text-gray-500">
                                        {{ $lesson->category->name_ar ?? 'غير محدد' }} - 
                                        @if($lesson->difficulty_level === 'beginner')
                                            <span class="text-green-600">مبتدئ</span>
                                        @elseif($lesson->difficulty_level === 'intermediate')
                                            <span class="text-yellow-600">متوسط</span>
                                        @else
                                            <span class="text-red-600">متقدم</span>
                                        @endif
                                    </div>
                                </div>
                                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">ابدأ التعلم</a>
                            </div>
                        @empty
                            <p class="text-gray-500 text-sm">لا توجد دروس مقترحة</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">تصفح الدروس</h4>
                                <p class="text-sm text-gray-500">اكتشف دروس جديدة</p>
                            </div>
                        </div>
                    </a>

                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">حل التمارين</h4>
                                <p class="text-sm text-gray-500">اختبر مهاراتك</p>
                            </div>
                        </div>
                    </a>

                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                </svg>
                            </div>
                            <div class="mr-3">
                                <h4 class="text-sm font-medium text-gray-900">الدروس المحفوظة</h4>
                                <p class="text-sm text-gray-500">راجع دروسك المفضلة</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
