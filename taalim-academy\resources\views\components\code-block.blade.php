<div class="code-block my-4">
    <div class="code-block-header flex justify-between items-center">
        <span>{{ $title }}</span>
        <button onclick="copyToClipboard(this)" class="text-xs bg-gray-600 hover:bg-gray-500 px-2 py-1 rounded">
            نسخ
        </button>
    </div>
    <pre class="language-{{ $language }} line-numbers" style="margin: 0;"><code class="language-{{ $language }}">{{ $code }}</code></pre>
</div>

<script>
function copyToClipboard(button) {
    const codeBlock = button.closest('.code-block').querySelector('code');
    const text = codeBlock.textContent;

    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = 'تم النسخ!';
        button.classList.add('bg-green-600');

        setTimeout(function() {
            button.textContent = originalText;
            button.classList.remove('bg-green-600');
        }, 2000);
    });
}
</script>