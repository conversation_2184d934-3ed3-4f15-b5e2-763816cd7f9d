<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-l from-blue-600 to-blue-800 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    🎓 الدورات التعليمية
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
                    اكتشف مجموعة واسعة من الدورات التعليمية المتخصصة في البرمجة والتكنولوجيا
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#courses" class="bg-white text-blue-700 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition-colors">
                        تصفح الدورات
                    </a>
                    <a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-700 transition-colors">
                        الدورات المجانية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div class="flex flex-wrap gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-3">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        جميع الدورات
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        مجانية
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        مدفوعة
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        مبتدئ
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        متقدم
                    </button>
                </div>
                <div class="flex items-center gap-3">
                    <select class="border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>ترتيب حسب</option>
                        <option>الأحدث</option>
                        <option>الأكثر شعبية</option>
                        <option>السعر</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Courses Grid -->
        <div id="courses" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Course Card 1 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=250&fit=crop" alt="دورة JavaScript" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">مجاني</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">25 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">مبتدئ</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">البرمجة</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">دورة JavaScript الشاملة</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">تعلم أساسيات وتقنيات JavaScript المتقدمة من الصفر حتى الاحتراف</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-medium text-sm">أ</span>
                            </div>
                            <span class="text-sm text-gray-600">أحمد محمد</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.8</span>
                            <span class="text-sm text-gray-500">(124)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            ابدأ الدورة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Course Card 2 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=250&fit=crop" alt="دورة Python" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">299 ر.س</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">40 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">متوسط</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">البرمجة</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">تطوير تطبيقات الويب بـ Python</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">تعلم بناء تطبيقات الويب الحديثة باستخدام Django و Flask</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-medium text-sm">س</span>
                            </div>
                            <span class="text-sm text-gray-600">سارة أحمد</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.9</span>
                            <span class="text-sm text-gray-500">(89)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            اشترك الآن
                        </a>
                    </div>
                </div>
            </div>

            <!-- Course Card 3 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=250&fit=crop" alt="دورة React" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">499 ر.س</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">35 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">متقدم</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">تطوير الويب</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">إتقان React و Next.js</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">بناء تطبيقات ويب حديثة وسريعة باستخدام React و Next.js</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 font-medium text-sm">م</span>
                            </div>
                            <span class="text-sm text-gray-600">محمد علي</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.7</span>
                            <span class="text-sm text-gray-500">(156)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            اشترك الآن
                        </a>
                    </div>
                </div>
            </div>

            <!-- Course Card 4 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=250&fit=crop" alt="دورة UI/UX" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">مجاني</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">20 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">مبتدئ</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">التصميم</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">أساسيات تصميم UI/UX</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">تعلم مبادئ تصميم واجهات المستخدم وتجربة المستخدم</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                                <span class="text-pink-600 font-medium text-sm">ل</span>
                            </div>
                            <span class="text-sm text-gray-600">ليلى حسن</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.6</span>
                            <span class="text-sm text-gray-500">(203)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            ابدأ الدورة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Course Card 5 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=400&h=250&fit=crop" alt="دورة DevOps" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">399 ر.س</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">45 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">متقدم</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">DevOps</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">DevOps و Cloud Computing</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">تعلم أدوات وممارسات DevOps والحوسبة السحابية</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                <span class="text-orange-600 font-medium text-sm">ع</span>
                            </div>
                            <span class="text-sm text-gray-600">عمر خالد</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.9</span>
                            <span class="text-sm text-gray-500">(67)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            اشترك الآن
                        </a>
                    </div>
                </div>
            </div>

            <!-- Course Card 6 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=250&fit=crop" alt="دورة Data Science" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 right-4">
                        <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">599 ر.س</span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">60 ساعة</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">متوسط</span>
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">علم البيانات</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">علم البيانات والذكاء الاصطناعي</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">تعلم تحليل البيانات والتعلم الآلي باستخدام Python</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                <span class="text-indigo-600 font-medium text-sm">د</span>
                            </div>
                            <span class="text-sm text-gray-600">د. فاطمة النور</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-star text-yellow-400"></i>
                            <span class="text-sm font-medium">4.8</span>
                            <span class="text-sm text-gray-500">(92)</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <a href="#" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block">
                            اشترك الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-12">
            <button class="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                عرض المزيد من الدورات
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\website\taalim-academy\resources\views/courses/index.blade.php ENDPATH**/ ?>